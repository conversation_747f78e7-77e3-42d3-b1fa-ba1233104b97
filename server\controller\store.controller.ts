import { Response, Request } from "express";
import { db } from "../lib/prisma";

export const addStore = async (req: Request, res: Response) => {
    try {
        const { name, domain, themeConfig, language, logo, currency } = req.body;
        const store = await db.store.create({
            data: {
                name,
                domain,
                themeConfig,
                language,
                logo,
                currency,
            },
        });
        res.status(200).json(store);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal server error" });
    }
}