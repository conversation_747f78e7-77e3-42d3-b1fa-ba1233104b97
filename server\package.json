{"name": "server", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only index.ts"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.4", "dependencies": {"@prisma/client": "6.11.1", "@types/cors": "^2.8.19", "cors": "^2.8.5", "dotenv-flow": "^4.1.0", "express": "^5.1.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.10", "prisma": "^6.11.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}