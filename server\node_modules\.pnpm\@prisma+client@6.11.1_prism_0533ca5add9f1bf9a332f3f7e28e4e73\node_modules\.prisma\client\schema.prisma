// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

type IThemeConfig {
  isDarkMode Boolean
}

type ILanguage {
  name String
  code String
}

model Store {
  id          String        @id @default(auto()) @map("_id") @db.ObjectId
  name        String        @unique
  domain      String        @unique
  themeConfig IThemeConfig?
  language    ILanguage[]
  logo        String?
  currency    String?
  deletedAt   DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
}
